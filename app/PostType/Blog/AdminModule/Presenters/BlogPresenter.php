<?php declare(strict_types = 1);

namespace App\PostType\Blog\AdminModule\Presenters;

use App\AdminModule\Presenters\BasePresenter;
use App\Model\Orm\User\User;
use App\PostType\Blog\AdminModule\Components\DataGrid\BlogDataGridPrescription;
use App\PostType\Blog\AdminModule\Components\Form\BlogFormPrescription;
use App\PostType\Blog\AdminModule\Components\Form\FormData\BaseFormData;
use App\PostType\Blog\Model\BlogLocalizationFacade;
use App\PostType\Blog\Model\BlogImportService;
use App\PostType\Blog\Model\BlogLocalizationImportParser;
use App\PostType\Blog\Model\Orm\BlogLocalization;
use App\PostType\Core\AdminModule\Components\DataGrid\DataGrid;
use App\PostType\Core\AdminModule\Components\DataGrid\DataGridFactory;
use App\PostType\Core\AdminModule\Components\Form\Form;
use App\PostType\Core\AdminModule\Components\Form\FormFactory;
use App\PostType\Core\AdminModule\Components\ShellForm\ShellForm;
use App\PostType\Core\AdminModule\Components\ShellForm\ShellFormFactory;
use Nette\Application\UI\Form as NetteForm;
use Nette\Http\FileUpload;
use Nette\Utils\JsonException;

final class BlogPresenter extends BasePresenter
{

	public const ORM_REPOSITORY_NAME = 'blog';

	private BlogLocalization $blogLocalization;

	public function __construct(
		private readonly DataGridFactory $dataGridFactory,
		private readonly FormFactory $formFactory,
		private readonly ShellFormFactory $shellFormFactory,
		private readonly BlogLocalizationFacade $blogLocalizationFacade,
		private readonly BlogFormPrescription $blogFormPrescription,
		private readonly BlogDataGridPrescription $blogDataGridPrescription,
		private readonly BlogImportService $blogImportService,
	)
	{
		parent::__construct();
	}

	public function renderDefault(): void
	{
	}


	public function actionEdit(int $id): void
	{
		$blog = $this->orm->blogLocalization->getById($id);
		if ($blog === null) {
			$this->redirect('default');
		}

		$this->blogLocalization = $blog;
	}


	public function renderEdit(int $id): void
	{
	}

	public function actionImport(): void
	{
	}

	public function renderImport(): void
	{
	}


	protected function createComponentGrid(): DataGrid
	{
		return $this->dataGridFactory->create(
			baseEntityName: self::ORM_REPOSITORY_NAME,
			collection: $this->orm->blogLocalization->findAll(),
			dataGridDefinition: $this->blogDataGridPrescription->get(),
		);
	}



	public function createComponentForm(): Form
	{
		/** @var User $userEntity */
		$userEntity = $this->userEntity;
		return $this->formFactory->create($this->blogLocalizationFacade, $this->blogLocalization, $userEntity, $this->blogFormPrescription->get($this->blogLocalization));
	}

	public function createComponentShellForm(): ShellForm
	{
		return $this->shellFormFactory->create(entity: null, entityLocalizationFacade: $this->blogLocalizationFacade);
	}

	public function createComponentImportForm(): NetteForm
	{
		$form = new NetteForm();

		$form->addUpload('jsonFile', 'JSON súbor:')
			->setRequired('Vyberte JSON súbor')
			->addRule(NetteForm::MAX_FILE_SIZE, 'Súbor je príliš veľký. Maximálna veľkosť je 16 MB.', 16 * 1024 * 1024);

		$form->addInteger('libraryTreeId', 'ID kategórie pre obrázky:')
			->setDefaultValue(1)
			->setRequired('Zadajte ID kategórie pre uloženie obrázkov')
			->addRule(NetteForm::MIN, 'ID kategórie musí byť kladné číslo', 1);

		$form->addSubmit('import', 'Importovať blogy');

		$form->onSuccess[] = $this->importFormSucceeded(...);

		return $form;
	}

	public function importFormSucceeded(NetteForm $form, \stdClass $values): void
	{
		/** @var FileUpload $file */
		$file = $values->jsonFile;

		if (!$file->isOk()) {
			$this->flashMessage('Chyba pri nahrávaní súboru.', 'error');
			return;
		}

		try {
			// Read and parse JSON
			$jsonContent = $file->getContents();
			$blogData = BlogLocalizationImportParser::parseJson($jsonContent);

			bd(count($blogData));
			// Import blogs
			$results = $this->blogImportService->importBlogs(
				$blogData,
				$this->mutationHolder->getMutation(),
				$this->userEntity,
				$values->libraryTreeId
			);

			// Show results
			$this->template->importResults = $results;
			$this->flashMessage(
				sprintf(
					'Import dokončený. Importované: %d, Preskočené: %d, Chyby: %d',
					$results['imported'],
					$results['skipped'],
					count($results['errors'])
				),
				$results['errors'] ? 'warning' : 'success'
			);

		} catch (JsonException $e) {
			$this->flashMessage('Neplatný JSON formát: ' . $e->getMessage(), 'error');
		} catch (\InvalidArgumentException $e) {
			$this->flashMessage('Chyba v štruktúre dát: ' . $e->getMessage(), 'error');
		} catch (\Exception $e) {
			$this->flashMessage('Chyba pri importe: ' . $e->getMessage(), 'error');
		}
	}

	public function beforeRender(): void
	{
		parent::beforeRender();
		$this->template->setFile(__DIR__ . '/../templates/' . $this->action . '.latte');
	}

}
