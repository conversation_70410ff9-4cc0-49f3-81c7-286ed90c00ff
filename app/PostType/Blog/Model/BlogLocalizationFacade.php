<?php declare(strict_types = 1);

namespace App\PostType\Blog\Model;

use App\PostType\Blog\Model\Dto\BlogLocalizationDto;
use App\PostType\Blog\Model\Orm\Blog;
use App\PostType\Blog\Model\Orm\BlogLocalization;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\PostType\Blog\Model\Orm\BlogLocalizationModel;
use App\PostType\Core\Model\EntityLocalizationFacade;
use App\PostType\Core\Model\LocalizationEntity;
use App\PostType\Core\Model\ParentEntity;
use Nette\Utils\ArrayHash;
use Nextras\Dbal\Utils\DateTimeImmutable;

final readonly class BlogLocalizationFacade implements EntityLocalizationFacade
{

	public function __construct(
		private Orm $orm,
		private BlogLocalizationModel $blogLocalizationModel,
	)
	{
	}


	public function create(Mutation $mutation, ParentEntity|null $localizableEntity): BlogLocalization
	{
		$blogLocalization = new BlogLocalization();
		$this->orm->blogLocalization->attach($blogLocalization);
		$blogLocalization->mutation = $mutation;

		if ($localizableEntity === null) {
			$localizableEntity = new Blog();
			$blogLocalization->blog = $localizableEntity;
		} else {
			assert($localizableEntity instanceof Blog);
			$blogLocalization->blog = $localizableEntity;
		}

		$this->orm->persistAndFlush($blogLocalization);

		return $blogLocalization;
	}

	public function put(BlogLocalization $blogLocalization, BlogLocalizationDto $dto): BlogLocalization
	{
		$blogLocalization->public = $dto->isPublic;
		$blogLocalization->name = $dto->name;
		$blogLocalization->nameAnchor = $dto->nameAnchor;
		$blogLocalization->nameTitle = $dto->nameTitle;
		$blogLocalization->cf =$dto->cf;
		$blogLocalization->cc = $dto->cc;
		$blogLocalization->blog->cf =$dto->commonCf;
		$blogLocalization->edited = $dto->editor->id;
		$blogLocalization->editedTime = new DateTimeImmutable();

		$this->orm->blogLocalization->persist($blogLocalization);

		$blogLocalization->setAlias($dto->alias);

		$blogLocalization->blog->tags->set($dto->tags);
		$blogLocalization->blog->authors->set($dto->authors);
		$this->blogLocalizationModel->setCategoriesByIds($blogLocalization, $dto->categories->fetchPairs(value: 'id'));

		$this->orm->blogLocalization->persistAndFlush($blogLocalization);

		return $blogLocalization;
	}

	public function remove(LocalizationEntity $localizableEntity): void
	{
		assert($localizableEntity instanceof BlogLocalization);

		$parent = $localizableEntity->getParent();
		assert($parent instanceof Blog);
		$this->orm->blogLocalization->remove($localizableEntity);

		if ($parent->getLocalizations()->count() === 0) {
			$this->orm->blog->remove($parent);
		}

		$this->orm->flush();
	}

}
